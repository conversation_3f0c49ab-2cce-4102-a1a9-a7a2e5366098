# src/train/callbacks.py
"""
DEPRECATED: This module has been split into separate files for better organization.

For backward compatibility, all classes are re-exported from their new locations.
Please update imports to use the new structure:

- Callback -> src.train.callbacks.base.Callback
- CallbackHandler -> src.train.callbacks.handler.CallbackHandler
- EarlyStoppingCallback -> src.train.callbacks.early_stopping.EarlyStoppingCallback
- ModelCheckpoint -> src.train.callbacks.model_checkpoint.ModelCheckpoint
- ModelRunCallback -> src.train.callbacks.model_run.ModelRunCallback
"""

# Re-export all classes for backward compatibility
from src.train.callbacks.base import Callback
from src.train.callbacks.early_stopping import EarlyStoppingCallback
from src.train.callbacks.handler import CallbackHandler
from src.train.callbacks.model_checkpoint import ModelCheckpoint
from src.train.callbacks.model_run import ModelRunCallback, ModelRunCallbackConfig

__all__ = [
    "Callback",
    "CallbackHand<PERSON>",
    "EarlyStoppingCallback",
    "<PERSON><PERSON>heckpoint",
    "Model<PERSON><PERSON><PERSON><PERSON>back",
    "ModelRunCallbackConfig",
]
