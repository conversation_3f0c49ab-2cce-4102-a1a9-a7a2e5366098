# src/train/callbacks/model_run.py
"""
Model run callback for database integration during training.
"""

import asyncio
import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, Optional, Union
from uuid import UUID

from src.train.callbacks.base import Callback

logger = logging.getLogger(__name__)

# Import database services at module level to avoid import-outside-toplevel
try:
    from src.database.services.model_run_service import (
        ModelRunCompleteUpdate,
        ModelRunMetricsUpdate,
        ModelRunService,
        ModelRunServiceError,
        ModelRunTimingUpdate,
    )

    DATABASE_SERVICES_AVAILABLE = True
except ImportError:
    # Create placeholder classes if imports fail
    ModelRunService = None
    ModelRunServiceError = Exception
    ModelRunTimingUpdate = None
    ModelRunMetricsUpdate = None
    ModelRunCompleteUpdate = None
    DATABASE_SERVICES_AVAILABLE = False


@dataclass
class ModelRunCallbackConfig:
    """Configuration for ModelRunCallback to reduce instance attributes."""

    model_run_uuid: str
    profile: Optional[str]
    update_frequency: int
    verbose: bool


class ModelRunCallback(Callback):
    """
    Callback to update model run data in the database during training.

    This callback integrates with the ModelRunService to update timing fields,
    metrics, and log paths throughout the training process.
    """

    def __init__(
        self,
        model_run_uuid: Union[str, UUID],
        profile: Optional[str] = None,
        *,
        update_frequency: int = 1,
        verbose: bool = True,
    ):
        """
        Initialize the ModelRunCallback.

        Args:
            model_run_uuid: UUID of the model run to update
            profile: Database profile to use (e.g., 'development', 'production')
            update_frequency: How often to update metrics (every N epochs)
            verbose: Whether to log update operations
        """
        super().__init__()

        # Use dataclass to group configuration
        self.config = ModelRunCallbackConfig(
            model_run_uuid=str(model_run_uuid),
            profile=profile,
            update_frequency=update_frequency,
            verbose=verbose,
        )
        self.start_time: Optional[datetime] = None

        # Check if database services are available
        if not DATABASE_SERVICES_AVAILABLE:
            logger.error("Database services are not available for ModelRunCallback")
            raise ImportError("Failed to import ModelRunService dependencies")

    def on_train_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of training."""
        self.start_time = datetime.now()

        if self.config.verbose:
            logger.info(
                "ModelRunCallback: Starting training for model run %s",
                self.config.model_run_uuid,
            )

        # Update start time in database
        self._run_async_update(self._update_start_time)

    def on_train_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of training."""
        end_time = datetime.now()

        if self.config.verbose:
            logger.info(
                "ModelRunCallback: Training completed for model run %s",
                self.config.model_run_uuid,
            )

        # Prepare final metrics from trainer
        final_metrics = self._prepare_final_metrics(logs)

        # Update end time, final metrics, and log path in database
        self._run_async_update(self._update_training_complete, end_time, final_metrics)

    def on_epoch_end(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of an epoch."""
        # Only update metrics every N epochs to avoid excessive database calls
        if (epoch + 1) % self.config.update_frequency == 0:
            if self.config.verbose:
                logger.debug(
                    "ModelRunCallback: Updating metrics for epoch %d, model run %s",
                    epoch + 1,
                    self.config.model_run_uuid,
                )

            # Prepare epoch metrics
            epoch_metrics = self._prepare_epoch_metrics(epoch, logs)

            # Update metrics in database
            self._run_async_update(self._update_epoch_metrics, epoch_metrics)

    def _run_async_update(self, coro_func, *args) -> None:
        """
        Run an async update function in the current event loop or create a new one.

        Args:
            coro_func: The async function to run
            *args: Arguments to pass to the function
        """
        try:
            # Try to get the current event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, schedule the coroutine as a task
                asyncio.create_task(coro_func(*args))
            else:
                # If no loop is running, run the coroutine
                loop.run_until_complete(coro_func(*args))
        except RuntimeError:
            # No event loop exists, create a new one
            asyncio.run(coro_func(*args))
        except Exception as e:
            logger.error(
                "ModelRunCallback: Error running async update for model run %s: %s",
                self.config.model_run_uuid,
                str(e),
            )

    async def _update_start_time(self) -> None:
        """Update the start time in the database."""
        try:
            update_data = ModelRunTimingUpdate(
                model_run_uuid=self.config.model_run_uuid,
                start_time=self.start_time,
                profile=self.config.profile,
            )
            await ModelRunService.update_model_run_times(update_data)

            if self.config.verbose:
                logger.info(
                    "ModelRunCallback: Updated start time for model run %s",
                    self.config.model_run_uuid,
                )
        except Exception as e:
            # Handle both ModelRunServiceError and other exceptions
            if isinstance(e, ModelRunServiceError):
                logger.error(
                    "ModelRunCallback: Failed to update start time for model run %s: %s",
                    self.config.model_run_uuid,
                    str(e),
                )
            else:
                logger.error(
                    "ModelRunCallback: Unexpected error updating start time for model run %s: %s",
                    self.config.model_run_uuid,
                    str(e),
                )

    async def _update_epoch_metrics(self, metrics: Dict[str, Any]) -> None:
        """Update epoch metrics in the database."""
        try:
            update_data = ModelRunMetricsUpdate(
                model_run_uuid=self.config.model_run_uuid,
                metrics=metrics,
                profile=self.config.profile,
            )
            await ModelRunService.update_model_run_metrics(update_data)

            if self.config.verbose:
                logger.debug(
                    "ModelRunCallback: Updated epoch metrics for model run %s",
                    self.config.model_run_uuid,
                )
        except Exception as e:
            # Handle both ModelRunServiceError and other exceptions
            if isinstance(e, ModelRunServiceError):
                logger.error(
                    "ModelRunCallback: Failed to update epoch metrics for model run %s: %s",
                    self.config.model_run_uuid,
                    str(e),
                )
            else:
                logger.error(
                    "ModelRunCallback: Unexpected error updating epoch metrics "
                    "for model run %s: %s",
                    self.config.model_run_uuid,
                    str(e),
                )

    async def _update_training_complete(
        self, end_time: datetime, final_metrics: Dict[str, Any]
    ) -> None:
        """Update training completion data in the database."""
        try:
            update_data = ModelRunCompleteUpdate(
                model_run_uuid=self.config.model_run_uuid,
                start_time=self.start_time,
                end_time=end_time,
                metrics=final_metrics,
                log_path=True,  # Indicate that logs are available
                profile=self.config.profile,
            )
            await ModelRunService.update_model_run_complete(update_data)

            if self.config.verbose:
                logger.info(
                    "ModelRunCallback: Updated training completion for model run %s",
                    self.config.model_run_uuid,
                )
        except Exception as e:
            # Handle both ModelRunServiceError and other exceptions
            if isinstance(e, ModelRunServiceError):
                logger.error(
                    "ModelRunCallback: Failed to update training completion for model run %s: %s",
                    self.config.model_run_uuid,
                    str(e),
                )
            else:
                logger.error(
                    "ModelRunCallback: Unexpected error updating training completion "
                    "for model run %s: %s",
                    self.config.model_run_uuid,
                    str(e),
                )

    def _prepare_epoch_metrics(
        self, epoch: int, logs: Dict[str, Any] | None
    ) -> Dict[str, Any]:
        """
        Prepare epoch metrics for database storage.

        Args:
            epoch: Current epoch number
            logs: Training logs from the epoch

        Returns:
            Dictionary containing formatted metrics
        """
        logs = logs or {}

        # Extract key metrics from logs
        metrics = {
            "epoch": epoch + 1,  # Convert to 1-based indexing
            "timestamp": datetime.now().isoformat(),
        }

        # Add training metrics if available
        if "train_loss" in logs:
            metrics["train_loss"] = float(logs["train_loss"])
        if "train_accuracy" in logs:
            metrics["train_accuracy"] = float(logs["train_accuracy"])

        # Add validation metrics if available
        if "validation_loss" in logs:
            metrics["validation_loss"] = float(logs["validation_loss"])
        if "validation_accuracy" in logs:
            metrics["validation_accuracy"] = float(logs["validation_accuracy"])

        # Add any additional metrics from logs
        for key, value in logs.items():
            if key not in metrics and isinstance(value, (int, float)):
                metrics[key] = float(value)

        return metrics

    def _prepare_final_metrics(self, logs: Dict[str, Any] | None) -> Dict[str, Any]:
        """
        Prepare final training metrics for database storage.

        Args:
            logs: Final training logs

        Returns:
            Dictionary containing formatted final metrics
        """
        logs = logs or {}

        # Get metrics from trainer if available
        final_metrics = {
            "training_completed": True,
            "completion_timestamp": datetime.now().isoformat(),
        }

        # Add final epoch metrics
        if logs:
            final_metrics.update(
                self._prepare_epoch_metrics(logs.get("epoch", 0), logs)
            )

        # Add trainer metrics if trainer is available
        if self.trainer and hasattr(self.trainer, "metrics"):
            trainer_metrics = self.trainer.get_metrics()

            # Add timing information
            if "timing" in trainer_metrics:
                timing = trainer_metrics["timing"]
                if "total_training_time" in timing:
                    final_metrics["total_training_time"] = timing["total_training_time"]
                if "epoch_times" in timing and timing["epoch_times"]:
                    final_metrics["average_epoch_time"] = sum(
                        timing["epoch_times"]
                    ) / len(timing["epoch_times"])

            # Add final accuracies and losses
            if "train_losses" in trainer_metrics and trainer_metrics["train_losses"]:
                final_metrics["final_train_loss"] = trainer_metrics["train_losses"][-1]
            if (
                "train_accuracies" in trainer_metrics
                and trainer_metrics["train_accuracies"]
            ):
                final_metrics["final_train_accuracy"] = trainer_metrics[
                    "train_accuracies"
                ][-1]
            if "test_losses" in trainer_metrics and trainer_metrics["test_losses"]:
                final_metrics["final_validation_loss"] = trainer_metrics["test_losses"][
                    -1
                ]
            if (
                "test_accuracies" in trainer_metrics
                and trainer_metrics["test_accuracies"]
            ):
                final_metrics["final_validation_accuracy"] = trainer_metrics[
                    "test_accuracies"
                ][-1]

            # Add error information if present
            if "error" in trainer_metrics and trainer_metrics["error"]:
                final_metrics["error"] = trainer_metrics["error"]

        return final_metrics
