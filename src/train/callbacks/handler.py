# src/train/callbacks/handler.py
"""
Callback handler for orchestrating multiple callbacks.
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict, List

from src.train.callbacks.base import Callback

if TYPE_CHECKING:
    from src.train.trainer import ModelTrainer


class CallbackHandler:
    """
    Handles a list of callbacks and orchestrates their execution.
    """

    def __init__(self, callbacks: List[Callback] | None, trainer: ModelTrainer):
        self.callbacks = callbacks if callbacks else []
        self._set_trainer_on_callbacks(trainer)

    def _set_trainer_on_callbacks(self, trainer: ModelTrainer) -> None:
        for callback in self.callbacks:
            callback.set_trainer(trainer)

    def on_train_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_train_begin hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_train_begin(logs)

    def on_train_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_train_end hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_train_end(logs)

    def on_epoch_begin(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_epoch_begin hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_epoch_begin(epoch, logs)

    def on_epoch_end(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_epoch_end hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_epoch_end(epoch, logs)

    def on_batch_begin(self, batch: int, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_batch_begin hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_batch_begin(batch, logs)

    def on_batch_end(self, batch: int, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_batch_end hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_batch_end(batch, logs)

    def on_validation_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_validation_begin hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_validation_begin(logs)

    def on_validation_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_validation_end hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_validation_end(logs)
