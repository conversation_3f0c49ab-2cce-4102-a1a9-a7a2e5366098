# src/train/callbacks/model_checkpoint.py
"""
Model checkpoint callback for saving model weights during training.
"""

import logging
from typing import Any, Dict

import torch

from src.train.callbacks.base import Callback

logger = logging.getLogger(__name__)


class ModelCheckpoint(Callback):
    """
    Callback to save the model checkpoint.

    This callback saves the model's weights at the end of every epoch
    if the monitored metric has improved.
    """

    def __init__(  # pylint: disable=too-many-arguments
        self,
        filepath: str,
        monitor: str = "validation_loss",
        mode: str = "min",
        *,
        save_best_only: bool = True,
        verbose: bool = True,
    ):
        super().__init__()
        self.filepath = filepath
        self.monitor = monitor
        self.mode = mode
        self.save_best_only = save_best_only
        self.verbose = verbose
        self.best_score = float("inf") if self.mode == "min" else float("-inf")

        if self.mode not in ["min", "max"]:
            raise ValueError(
                f"ModelCheckpoint mode '{self.mode}' is unknown, use 'min' or 'max'."
            )

    def on_epoch_end(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        logs = logs or {}
        current_score = logs.get(self.monitor)

        if current_score is None:
            logger.warning(
                "ModelCheckpoint conditioned on unavailable metric `%s`. Available metrics are: %s",
                self.monitor,
                ",".join(logs.keys()),
            )
            return

        if self.mode == "min":
            improved = current_score < self.best_score
        else:  # mode == "max"
            improved = current_score > self.best_score

        if improved:
            previous_best = self.best_score
            self.best_score = current_score

            if self.trainer and self.trainer.components.model:
                if self.verbose:
                    logger.info(
                        "Epoch %d: %s improved from %.4f to %.4f, saving model to %s",
                        epoch,
                        self.monitor,
                        previous_best,
                        self.best_score,
                        self.filepath,
                    )
                torch.save(self.trainer.components.model.state_dict(), self.filepath)
