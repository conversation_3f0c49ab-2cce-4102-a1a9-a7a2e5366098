# src/train/callbacks/__init__.py
"""
Callback system for the ModelTrainer.

This package provides a modular callback system with the following components:

- base.Callback: Abstract base class for all callbacks
- handler.CallbackHandler: Orchestrates execution of multiple callbacks
- early_stopping.EarlyStoppingCallback: Stops training when metrics stop improving
- model_checkpoint.ModelCheckpoint: Saves model weights when metrics improve
- model_run.ModelRunCallback: Integrates with database for training tracking

All classes are re-exported at the package level for convenience.
"""

from src.train.callbacks.base import Callback
from src.train.callbacks.early_stopping import EarlyStoppingCallback
from src.train.callbacks.handler import Callback<PERSON>andler
from src.train.callbacks.model_checkpoint import ModelCheckpoint
from src.train.callbacks.model_run import ModelRun<PERSON><PERSON>back, ModelRunCallbackConfig

__all__ = [
    "Callback",
    "CallbackHandler",
    "EarlyStoppingCallback", 
    "ModelCheckpoint",
    "ModelRunCallback",
    "ModelRunCallbackConfig",
]
