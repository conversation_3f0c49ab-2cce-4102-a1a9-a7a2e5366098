# src/train/callbacks/__init__.py
"""
Callback system for the ModelTrainer.

This package provides a modular callback system with the following components:

- base.Callback: Abstract base class for all callbacks
- handler.CallbackHandler: Orchestrates execution of multiple callbacks
- early_stopping.EarlyStoppingCallback: Stops training when metrics stop improving
- model_checkpoint.ModelCheckpoint: Saves model weights when metrics improve
- model_run.ModelRunCallback: Integrates with database for training tracking

Only the base classes are re-exported for convenience. Import specific callbacks
from their individual modules:

    from src.train.callbacks.early_stopping import EarlyStoppingCallback
    from src.train.callbacks.model_checkpoint import ModelCheckpoint
    from src.train.callbacks.model_run import ModelRunCallback
"""

from src.train.callbacks.base import Callback
from src.train.callbacks.handler import CallbackHandler

__all__ = [
    "Callback",
    "CallbackHandler",
]
