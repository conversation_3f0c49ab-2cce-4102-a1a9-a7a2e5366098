"""
Training Data Service for preparing and transforming data for model training.
"""

from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>, TypedDict
from uuid import UUID

from pydantic import BaseModel, ConfigDict

from database.models.dataset import Dataset
from database.models.version import ModelVersion
from database.services.base_service import BaseService
from database.services.dataset_service import DatasetError, DatasetService
from database.services.model_metadata_service import (
    ModelMetadataError,
    ModelMetadataService,
)
from utils.async_utils import maybe_await


class TrainingDataError(Exception):
    """Base exception for training data related errors."""


class TrainingDataModel(BaseModel):
    """Structured training data model."""

    model: Dict
    model_version: Dict
    dataset: Dict
    model_run: Dict
    training_parameters: Dict
    dataset_metadata: Dict

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "model": {"uuid": "123", "name": "Example Model"},
                "model_version": {"uuid": "456", "version": "1.0.0"},
                "dataset": {"uuid": "789", "name": "Example Dataset"},
                "model_run": {"uuid": "abc", "status": "pending"},
                "training_parameters": {"batch_size": 32, "epochs": 10},
                "dataset_metadata": {"images_count": 1000},
            }
        }
    )


# For backward compatibility
TrainingData = TypedDict(
    "TrainingData",
    {
        "model": Dict,
        "model_version": Dict,
        "dataset": Dict,
        "model_run": Dict,
        "training_parameters": Dict,
        "dataset_metadata": Dict,
    },
)


class TrainingDataService(BaseService[TrainingDataModel]):
    """Service for preparing and transforming data for model training."""

    # Set the model class for this service
    model_class = TrainingDataModel

    # Define required fields for validation
    required_fields = ["model", "model_version", "dataset", "model_run"]

    @classmethod
    async def get_training_data(
        cls,
        model_run_uuid: str | UUID,
        profile: Optional[str] = None,
    ) -> TrainingData:
        """
        Prepare all required data for model training using model run UUID.

        Args:
            model_run_uuid: UUID of the model run containing training configuration
            profile: Optional Supabase profile name

        Returns:
            Dictionary containing all required training data

        Raises:
            TrainingDataError: If required data is missing or invalid
        """
        try:
            # Get model metadata using ModelMetadataService
            model_metadata_result = ModelMetadataService.get_model_metadata(
                model_run_uuid=model_run_uuid,
                profile=profile,
            )

            # Safely await the result
            model_metadata = await maybe_await(model_metadata_result)

            # Extract dataset UUID from model run
            dataset_uuid = model_metadata["model_run"].get("dataset_uuid")
            if not dataset_uuid:
                raise TrainingDataError(
                    f"Dataset UUID not found in model run {model_run_uuid}"
                )

            # Fetch and validate dataset
            try:
                dataset_result = DatasetService.get_dataset(dataset_uuid, profile)
                dataset_data = await maybe_await(dataset_result)
            except DatasetError as e:
                raise TrainingDataError(f"Failed to fetch dataset: {str(e)}") from e

            # Get training parameters from model version or model run
            training_parameters = model_metadata["model_version"].get("parameters", {})
            # Merge with any parameters from model run if they exist
            if "parameters" in model_metadata["model_run"] and isinstance(
                model_metadata["model_run"]["parameters"], dict
            ):
                training_parameters.update(model_metadata["model_run"]["parameters"])

            # Prepare dataset metadata
            dataset_metadata = cls._prepare_dataset_metadata(dataset_data)

            # Construct the training data dictionary
            training_data = {
                "model": model_metadata["model"],
                "model_version": model_metadata["model_version"],
                "dataset": dataset_data,
                "model_run": model_metadata["model_run"],
                "training_parameters": training_parameters,
                "dataset_metadata": dataset_metadata,
            }

            # Validate the training data
            try:
                model = cls.create_model(data=training_data)
                return model.model_dump()
            except Exception as e:
                raise TrainingDataError(f"Invalid training data: {str(e)}") from e
        except ModelMetadataError as e:
            raise TrainingDataError(str(e)) from e

    @classmethod
    def _prepare_dataset_metadata(cls, dataset_data: Dict) -> Dict:
        """
        Prepare dataset metadata for training.

        Args:
            dataset_data: Raw dataset data from the database

        Returns:
            Dictionary containing prepared dataset metadata

        Note:
            The dataset_data is already validated by DatasetService
        """
        return {
            "name": dataset_data["name"],
            "description": dataset_data.get("description", ""),
            "images_count": dataset_data["images_count"],
            "created_at": dataset_data.get("created_at"),
            "content_updated_at": dataset_data.get("content_updated_at"),
        }

    @classmethod
    async def update_model_run_status(
        cls,
        model_run_uuid: str | UUID,
        status: str,
        metrics: Optional[Dict] = None,
        profile: Optional[str] = None,
    ) -> bool:
        """
        Update the status of a model run.

        Args:
            model_run_uuid: UUID of the model run to update
            status: New status for the model run
            metrics: Optional metrics to include in the update
            profile: Optional Supabase profile name

        Returns:
            bool: True if the update was successful

        Note:
            This is a convenience method that delegates to ModelMetadataService.
        """
        return await ModelMetadataService.update_model_run_status(
            model_run_uuid=model_run_uuid,
            status=status,
            metrics=metrics,
            profile=profile,
        )

    @classmethod
    async def prepare_training_data(
        cls, training_data: TrainingData
    ) -> Tuple[ModelVersion, Dataset, str]:
        """
        Prepare the training data for model training.

        Transforms raw dictionary data into proper model objects and performs validation.
        This centralizes the object creation logic that was previously in the API routes.

        Args:
            training_data: The raw training data from get_training_data()

        Returns:
            Tuple containing (model_version, dataset, experiment_uuid)

        Raises:
            TrainingDataError: If validation fails or required data is missing
        """
        try:
            # Extract data from the training data
            model_version_data = training_data["model_version"]
            dataset_data = training_data["dataset"]
            model_run_data = training_data["model_run"]

            # Clean up parameters to ensure they're valid for Pydantic validation
            # Convert empty strings to None for numeric fields
            if "parameters" in model_version_data:
                params = model_version_data["parameters"]
                numeric_fields = [
                    "residual_blocks",
                    "dense_blocks",
                    "transition_layers",
                    "resolution_multiplier",
                    "width_multiplier",
                    "growth_rate",
                ]

                for field in numeric_fields:
                    if field in params and params[field] == "":
                        params[field] = None

            # Create model version and dataset objects
            model_version = ModelVersion(**model_version_data)
            dataset = Dataset(**dataset_data)

            # Ensure experiment_uuid is a valid string, defaulting to empty string if None
            experiment_uuid = model_run_data.get("experiment_uuid") or ""

            return model_version, dataset, experiment_uuid

        except KeyError as e:
            raise TrainingDataError(
                f"Missing required data in training data: {str(e)}"
            ) from e
        except Exception as e:
            raise TrainingDataError(f"Error preparing training data: {str(e)}") from e
