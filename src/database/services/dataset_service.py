"""
Dataset Service for fetching and managing dataset-related data.
"""

import logging
import traceback
from pathlib import Path
from typing import Dict, Generator, List, Optional, Tuple, Union
from uuid import UUID

# Import directly from the model files to avoid circular imports
from database.models.dataset_set import DatasetSet, SetType
from database.services.base_service import BaseService
from database.supabase_client import fetch_data
from database.utils.file_utils import download_file
from database.utils.model_factory import ModelValidationError

# Configure logger
logger = logging.getLogger(__name__)


class DatasetError(Exception):
    """Base exception for dataset related errors."""


class DatasetService(BaseService[DatasetSet]):
    """Service for fetching and managing dataset data."""

    # Set the model class for this service
    model_class = DatasetSet

    # Define required fields for validation
    required_fields = ["uuid", "name", "images_count"]

    @classmethod
    def get_dataset(
        cls,
        dataset_uuid: str | UUID,
        profile: Optional[str] = None,
    ) -> Dict:
        """
        Fetch dataset data by UUID.

        Args:
            dataset_uuid: UUID of the dataset
            profile: Optional Supabase profile name

        Returns:
            Dictionary containing dataset data

        Raises:
            DatasetError: If dataset is not found or data is invalid
        """
        dataset = cls._fetch_dataset(dataset_uuid, profile)
        cls._validate_dataset(dataset)
        return dataset

    @classmethod
    def _fetch_dataset(
        cls, dataset_uuid: str | UUID, profile: Optional[str] = None
    ) -> Dict:
        """
        Fetch raw dataset data from the database.

        Args:
            dataset_uuid: UUID of the dataset
            profile: Optional Supabase profile name

        Returns:
            Dictionary containing raw dataset data

        Raises:
            DatasetError: If dataset is not found
        """
        datasets = fetch_data(
            "datasets",
            {"filters": [{"column": "uuid", "value": str(dataset_uuid)}]},
            profile=profile,
        )
        if not datasets:
            raise DatasetError(f"Dataset not found with UUID: {dataset_uuid}")
        return datasets[0]

    @classmethod
    def _validate_dataset(cls, dataset_data: Dict) -> None:
        """
        Validate dataset data.

        Args:
            dataset_data: Dataset data to validate

        Raises:
            DatasetError: If dataset data is invalid
        """
        try:
            # Define custom validators
            field_validators = {
                "images_count": lambda value: (isinstance(value, int) and value > 0)
                or ValueError("Dataset must contain at least one image")
            }

            # Use the generic validation method
            cls.validate_data(data=dataset_data, field_validators=field_validators)
        except ModelValidationError as e:
            # Convert ModelValidationError to DatasetError
            raise DatasetError(str(e)) from e

    @classmethod
    def get_dataset_sets(
        cls,
        dataset_uuid: Union[str, UUID],
        set_type: Optional[SetType] = None,
        batch_size: int = 1000,
        profile: Optional[str] = None,
    ) -> Generator[List[DatasetSet], None, None]:
        """
        Fetch dataset sets with image URLs in batches.

        Args:
            dataset_uuid: UUID of the dataset
            set_type: Optional set type to filter by
            batch_size: Number of records to fetch in each batch
                (must be a positive integer)
            profile: Optional Supabase profile name

        Yields:
            List of DatasetSet objects for the current batch

        Raises:
            ValueError: If batch_size is not a positive integer
            DatasetError: If there's an error fetching the data
        """
        # Validate batch_size
        if not isinstance(batch_size, int) or batch_size <= 0:
            raise ValueError("Batch size must be a positive integer")
        # First, get the total count to handle pagination
        count_query = {
            "filters": [{"column": "dataset_uuid", "value": str(dataset_uuid)}],
            "count": "exact",
        }
        if set_type is not None:
            count_query["filters"].append(
                {"column": "set_type", "value": int(set_type)}
            )

        count_result = fetch_data(
            "dataset_sets",
            count_query,
            profile=profile,
        )

        if not count_result or "count" not in count_result[0]:
            raise DatasetError(f"Failed to get count for dataset {dataset_uuid}")

        total_count = count_result[0]["count"]
        if total_count == 0:
            return

        # Process in batches
        offset = 0

        while offset < total_count:
            # Fetch the batch
            batch_config = cls.BatchConfig(
                dataset_uuid=dataset_uuid,
                set_type=set_type,
                offset=offset,
                limit=batch_size,
                profile=profile,
            )
            batch = cls._fetch_dataset_sets_batch(config=batch_config)
            if batch:
                yield batch
                offset += len(batch)
            else:
                break

    class BatchConfig:  # pylint: disable=too-few-public-methods
        """Configuration class for dataset batch fetching."""

        def __init__(  # pylint: disable=too-many-arguments
            self,
            dataset_uuid: Union[str, UUID],
            *,  # Force keyword arguments for the rest
            set_type: Optional[SetType] = None,
            offset: int = 0,
            limit: int = 1000,
            profile: Optional[str] = None,
        ):
            self.dataset_uuid = dataset_uuid
            self.set_type = set_type
            self.offset = offset
            self.limit = limit
            self.profile = profile

    @classmethod
    def _fetch_dataset_sets_batch(
        cls,
        config: BatchConfig,
    ) -> List[DatasetSet]:
        """
        Fetch a single batch of dataset sets with image URLs.

        Args:
            config: BatchConfig object containing query parameters
                - dataset_uuid: UUID of the dataset
                - set_type: Optional set type to filter by
                - offset: Offset for pagination
                - limit: Maximum number of records to return
                - profile: Optional Supabase profile name

        Returns:
            List of DatasetSetWithImage objects
        """
        # Build the base query
        query = {
            "select": "*, images_reviews(image_url)",
            "filters": [
                {"column": "dataset_uuid", "value": str(config.dataset_uuid)},
                {"column": "image_uuid", "operator": "is_not", "value": None},
            ],
            "limit": config.limit,
            "offset": config.offset,
            "order_by": [
                {"column": "created_at", "ascending": True}
            ],  # Fixed long line
        }

        # Add set type filter if specified
        if config.set_type is not None:
            query["filters"].append(
                {"column": "set_type", "value": int(config.set_type)}
            )

        # Execute the query with join to images_reviews
        results = fetch_data(
            "dataset_sets",
            query,
            profile=config.profile,
        )

        # Process and validate results
        dataset_sets = []
        for item in results:
            try:
                # Extract image_url from the joined images_reviews table
                image_url = None
                if "images_reviews" in item and item["images_reviews"]:
                    # Handle case where images_reviews is a list of dicts
                    if (
                        isinstance(item["images_reviews"], list)
                        and len(item["images_reviews"]) > 0
                    ):
                        # Get the first review if available
                        first_review = item["images_reviews"][0]
                        if isinstance(first_review, dict):
                            image_url = first_review.get("image_url")
                    # Handle case where it's a single dict (for backward compatibility)
                    elif isinstance(item["images_reviews"], dict):
                        image_url = item["images_reviews"].get("image_url")

                # Create the dataset set with image URL using the generic model creation
                try:
                    # Prepare data dictionary
                    data_dict = {
                        "uuid": item["uuid"],
                        "dataset_uuid": item["dataset_uuid"],
                        "created_at": item.get("created_at"),
                        "coin_side_uuid": item.get("coin_side_uuid"),
                        "set_type": item.get("set_type"),
                        "image_uuid": item.get("image_uuid"),
                        "image_url": image_url,
                    }

                    # Create model instance
                    dataset_set = cls.create_model(
                        data=data_dict, model_class=DatasetSet, partial=True
                    )
                    logger.debug("Created DatasetSet: %s", dataset_set)
                    dataset_sets.append(dataset_set)
                except ModelValidationError as e:
                    # Skip invalid records but log the error
                    logger.warning("Skipping invalid dataset set record: %s", str(e))
                    traceback.print_exc()
                    continue
            except (KeyError, ValueError) as e:
                # Skip invalid records but log the error
                logger.warning("Skipping invalid dataset set record: %s", str(e))
                traceback.print_exc()
                continue

        return dataset_sets

    @classmethod
    def download_dataset_images(
        cls,
        dataset_uuid: Union[str, UUID],
        base_output_dir: Union[str, Path],
        set_type: Optional[SetType] = None,
        profile: Optional[str] = None,
    ) -> Tuple[int, List[str]]:
        """
        Download all images for a dataset to the local filesystem.

        Args:
            dataset_uuid: UUID of the dataset
            base_output_dir: Base directory to save the downloaded images
            set_type: Optional set type to filter by
            profile: Optional Supabase profile name

        Returns:
            Tuple of (downloaded_count, error_messages)
        """
        base_output_dir = Path(base_output_dir)
        downloaded_count = 0
        error_messages = []

        # Process dataset sets in batches
        try:
            for batch in cls.get_dataset_sets(
                dataset_uuid=dataset_uuid,
                set_type=set_type,
                batch_size=100,  # Smaller batch size for memory usage
                profile=profile,
            ):
                # Download images
                for dataset_set in batch:
                    if not dataset_set.image_url or not dataset_set.coin_side_uuid:
                        continue
                    # Create output directory structure
                    # Format: {base_output_dir}/{coin_side_uuid}/
                    coin_side_id = str(dataset_set.coin_side_uuid)
                    output_dir = base_output_dir / coin_side_id
                    output_dir.mkdir(parents=True, exist_ok=True)
                    # Generate a safe filename from the image URL
                    filename = f"{dataset_set.image_uuid}.jpg"  # Assuming JPG
                    # Download image
                    cls._download_image(
                        image_url=dataset_set.image_url,
                        output_dir=output_dir,
                        filename=filename,
                    )
                    downloaded_count += 1
        except Exception as e:
            error_messages.append(f"Error processing dataset: {str(e)}")
        return downloaded_count, error_messages

    @classmethod
    def _download_image(
        cls,
        image_url: str,
        output_dir: Path,
        filename: str,
    ) -> Optional[Path]:
        """
        Download an image.

        Args:
            image_url: URL of the image to download
            output_dir: Directory to save the image
            filename: Name of the output file

        Returns:
            Path of the downloaded image or None if failed
        """
        try:
            return download_file(
                url=image_url,
                output_dir=output_dir,
                filename=filename,
                timeout=60,
            )
        except Exception as e:
            logger.warning("Failed to download image %s: %s", image_url, str(e))
            return None
