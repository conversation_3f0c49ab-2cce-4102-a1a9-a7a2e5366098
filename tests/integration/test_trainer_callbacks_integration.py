"""
Integration tests for ModelTrainer with callback system.

These tests focus on the integration between ModelTrainer and:
- src.train.callbacks (<PERSON><PERSON>, CallbackHandler, EarlyStoppingCallback, ModelRunCallback)

The tests verify that the callback system works correctly with the trainer in realistic scenarios.
"""

import tempfile
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest
import torch
from torch import nn, optim
from torch.utils.data import DataLoader, TensorDataset

from src.train.callbacks import Callback
from src.train.callbacks.early_stopping import EarlyStoppingCallback
from src.train.trainer import DatabaseConfig, ModelTrainer, TrainerConfig


class SimpleCallbackModel(nn.Module):
    """A simple model for testing callback integration."""

    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(4, 1)

    def forward(self, x):
        """Forward pass of the model."""
        return self.linear(x)


class CustomTestCallback(Callback):
    """A custom callback for testing integration."""

    def __init__(self):
        super().__init__()
        self.events = []

    def on_train_begin(self, logs=None):
        """Record train begin event."""
        self.events.append("train_begin")

    def on_train_end(self, logs=None):
        """Record train end event."""
        self.events.append("train_end")

    def on_epoch_begin(self, epoch, logs=None):
        """Record epoch begin event."""
        self.events.append(f"epoch_begin_{epoch}")

    def on_epoch_end(self, epoch, logs=None):
        """Record epoch end event."""
        self.events.append(f"epoch_end_{epoch}")

    def on_batch_begin(self, batch, logs=None):
        """Record batch begin event."""
        self.events.append(f"batch_begin_{batch}")

    def on_batch_end(self, batch, logs=None):
        """Record batch end event."""
        self.events.append(f"batch_end_{batch}")


@pytest.fixture(name="callback_test_components")
def fixture_callback_test_components():
    """Create components for callback integration testing."""
    torch.manual_seed(42)
    model = SimpleCallbackModel()
    loss_fn = nn.BCEWithLogitsLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    return {"model": model, "loss_fn": loss_fn, "optimizer": optimizer}


@pytest.fixture(name="callback_test_loaders")
def fixture_callback_test_loaders():
    """Create data loaders for callback integration testing."""
    torch.manual_seed(42)
    x_train = torch.randn(16, 4)
    y_train = torch.randint(0, 2, (16, 1)).float()
    x_test = torch.randn(8, 4)
    y_test = torch.randint(0, 2, (8, 1)).float()

    train_dataset = TensorDataset(x_train, y_train)  # pylint: disable=duplicate-code
    test_dataset = TensorDataset(x_test, y_test)

    train_loader = DataLoader(train_dataset, batch_size=4)
    test_loader = DataLoader(test_dataset, batch_size=4)

    return {"train": train_loader, "test": test_loader}


class TestCallbackHandlerIntegration:
    """Integration tests for CallbackHandler with ModelTrainer."""

    def test_callback_handler_lifecycle_integration(
        self, callback_test_components, callback_test_loaders
    ):
        """Test that CallbackHandler properly manages callback lifecycle."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_callback_lifecycle",
                "run_output_dir": temp_dir,
                "epochs": 2,
            }

            # Create custom callback to track events
            custom_callback = CustomTestCallback()

            config = TrainerConfig(
                model_components=callback_test_components,
                data_loaders=callback_test_loaders,
                training_config=training_config,
                callbacks=[custom_callback],
            )

            trainer = ModelTrainer(config)

            # Verify callback was integrated
            assert len(trainer.callback_handler.callbacks) == 1
            assert trainer.callback_handler.callbacks[0] == custom_callback
            assert custom_callback.trainer == trainer

            # Run training to trigger callback events
            with patch.object(trainer, "_collect_resource_metrics"):
                trainer.train()

            # Verify all expected events were triggered
            expected_events = [
                "train_begin",
                "epoch_begin_0",
                "epoch_end_0",
                "epoch_begin_1",
                "epoch_end_1",
                "train_end",
            ]

            for event in expected_events:
                assert event in custom_callback.events

    def test_multiple_callbacks_integration(
        self, callback_test_components, callback_test_loaders
    ):
        """Test integration with multiple callbacks."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_multiple_callbacks",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            # Create multiple callbacks
            callback1 = CustomTestCallback()
            callback2 = CustomTestCallback()
            early_stopping = EarlyStoppingCallback(patience=5, verbose=False)

            config = TrainerConfig(
                model_components=callback_test_components,
                data_loaders=callback_test_loaders,
                training_config=training_config,
                callbacks=[callback1, callback2, early_stopping],
            )

            trainer = ModelTrainer(config)

            # Verify all callbacks were integrated
            assert len(trainer.callback_handler.callbacks) == 3
            assert callback1 in trainer.callback_handler.callbacks
            assert callback2 in trainer.callback_handler.callbacks
            assert early_stopping in trainer.callback_handler.callbacks

            # Verify all callbacks have trainer reference
            for callback in trainer.callback_handler.callbacks:
                assert callback.trainer == trainer

            # Run training
            with patch.object(trainer, "_collect_resource_metrics"):
                trainer.train()

            # Verify both custom callbacks received events
            assert "train_begin" in callback1.events
            assert "train_begin" in callback2.events
            assert "train_end" in callback1.events
            assert "train_end" in callback2.events


class TestEarlyStoppingIntegration:
    """Integration tests for EarlyStoppingCallback with ModelTrainer."""

    def test_early_stopping_integration_with_improvement(
        self, callback_test_components, callback_test_loaders
    ):
        """Test early stopping when there's continuous improvement."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_early_stopping_improvement",
                "run_output_dir": temp_dir,
                "epochs": 5,
            }

            early_stopping = EarlyStoppingCallback(
                monitor="validation_loss", patience=2, verbose=False
            )

            config = TrainerConfig(
                model_components=callback_test_components,
                data_loaders=callback_test_loaders,
                training_config=training_config,
                callbacks=[early_stopping],
            )

            trainer = ModelTrainer(config)

            # Mock validation to show continuous improvement
            improving_losses = [0.8, 0.6, 0.4, 0.2, 0.1]
            side_effects = [
                {"validation_loss": loss, "validation_accuracy": 0.5}
                for loss in improving_losses
            ]

            with patch.object(
                trainer, "_validate_epoch", side_effect=side_effects
            ), patch.object(
                trainer,
                "_train_epoch",
                return_value={"train_loss": 0.5, "train_accuracy": 0.8},
            ), patch.object(
                trainer, "_collect_resource_metrics"
            ):

                metrics = trainer.train()

                # Should complete all epochs since there's continuous improvement
                assert len(metrics.train_losses) == 5
                assert trainer.stop_training is False

    def test_early_stopping_integration_with_no_improvement(
        self, callback_test_components, callback_test_loaders
    ):
        """Test early stopping when there's no improvement."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_early_stopping_no_improvement",
                "run_output_dir": temp_dir,
                "epochs": 10,
            }

            early_stopping = EarlyStoppingCallback(
                monitor="validation_loss", patience=2, verbose=False
            )

            config = TrainerConfig(
                model_components=callback_test_components,
                data_loaders=callback_test_loaders,
                training_config=training_config,
                callbacks=[early_stopping],
            )

            trainer = ModelTrainer(config)

            # Mock validation to show no improvement after first epoch
            no_improvement_losses = [0.5, 0.6, 0.7, 0.8, 0.9]
            side_effects = [
                {"validation_loss": loss, "validation_accuracy": 0.5}
                for loss in no_improvement_losses
            ]

            with patch.object(
                trainer, "_validate_epoch", side_effect=side_effects
            ), patch.object(
                trainer,
                "_train_epoch",
                return_value={"train_loss": 0.5, "train_accuracy": 0.8},
            ), patch.object(
                trainer, "_collect_resource_metrics"
            ):

                metrics = trainer.train()

                # Should stop early after patience is exhausted
                # First epoch sets baseline, next 2 epochs don't improve, so stops at epoch 3
                assert len(metrics.train_losses) == 3
                assert trainer.stop_training is True


class TestModelRunCallbackIntegration:
    """Integration tests for ModelRunCallback with ModelTrainer."""

    @patch("src.train.trainer.ModelRunCallback")
    def test_model_run_callback_integration_success(
        self,
        mock_callback_class,
        callback_test_components,
        callback_test_loaders,
    ):
        """Test successful integration of ModelRunCallback."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_model_run_callback",
                "run_output_dir": temp_dir,
                "epochs": 2,
            }

            # Setup mock callback
            mock_callback_instance = MagicMock()
            mock_callback_class.return_value = mock_callback_instance

            model_run_uuid = str(uuid4())
            database_config = DatabaseConfig(
                model_run_uuid=model_run_uuid, profile="development"
            )

            config = TrainerConfig(
                model_components=callback_test_components,
                data_loaders=callback_test_loaders,
                training_config=training_config,
                database_config=database_config,
            )

            trainer = ModelTrainer(config)

            # Verify ModelRunCallback was created with correct parameters
            mock_callback_class.assert_called_once_with(
                model_run_uuid=model_run_uuid,
                profile="development",
                verbose=True,
            )

            # Verify callback was added to handler
            assert mock_callback_instance in trainer.callback_handler.callbacks

            # Run training to trigger callback methods
            with patch.object(trainer, "_collect_resource_metrics"):
                trainer.train()

            # Verify callback lifecycle methods were called
            mock_callback_instance.on_train_begin.assert_called_once()
            mock_callback_instance.on_train_end.assert_called_once()
            assert mock_callback_instance.on_epoch_end.call_count == 2

    @patch("src.train.trainer.ModelRunCallback")
    def test_model_run_callback_integration_with_other_callbacks(
        self,
        mock_callback_class,
        callback_test_components,
        callback_test_loaders,
    ):
        """Test ModelRunCallback integration alongside other callbacks."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_mixed_callbacks",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            # Setup mock ModelRunCallback
            mock_callback_instance = MagicMock()
            mock_callback_class.return_value = mock_callback_instance

            # Create other callbacks
            custom_callback = CustomTestCallback()
            early_stopping = EarlyStoppingCallback(patience=5, verbose=False)

            database_config = DatabaseConfig(
                model_run_uuid=str(uuid4()), profile="staging"
            )

            config = TrainerConfig(
                model_components=callback_test_components,
                data_loaders=callback_test_loaders,
                training_config=training_config,
                callbacks=[custom_callback, early_stopping],
                database_config=database_config,
            )

            trainer = ModelTrainer(config)

            # Verify all callbacks were integrated
            # Should have: custom_callback, early_stopping, + ModelRunCallback
            assert len(trainer.callback_handler.callbacks) == 3
            assert custom_callback in trainer.callback_handler.callbacks
            assert early_stopping in trainer.callback_handler.callbacks
            assert mock_callback_instance in trainer.callback_handler.callbacks

            # Run training
            with patch.object(trainer, "_collect_resource_metrics"):
                trainer.train()

            # Verify all callbacks were called
            mock_callback_instance.on_train_begin.assert_called_once()
            assert "train_begin" in custom_callback.events

    @patch("src.train.trainer.ModelRunCallback")
    def test_model_run_callback_import_error_integration(
        self,
        mock_callback_class,
        callback_test_components,
        callback_test_loaders,
    ):
        """Test handling of ModelRunCallback import errors."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_callback_import_error",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            # Simulate import error
            mock_callback_class.side_effect = ImportError("Database not available")

            database_config = DatabaseConfig(
                model_run_uuid=str(uuid4()), profile="development"
            )

            # Mock logger to verify warning
            with patch("src.train.trainer.setup_training_logger") as mock_setup_logger:
                mock_logger = MagicMock()
                mock_setup_logger.return_value = mock_logger

                config = TrainerConfig(
                    model_components=callback_test_components,
                    data_loaders=callback_test_loaders,
                    training_config=training_config,
                    database_config=database_config,
                )

                trainer = ModelTrainer(config)

                # Verify warning was logged
                mock_logger.warning.assert_called_once()
                warning_call = mock_logger.warning.call_args[0][0]
                assert "Failed to import ModelRunCallback" in warning_call

                # Verify trainer still works without the callback
                assert len(trainer.callback_handler.callbacks) == 0

                # Verify training still works
                with patch.object(trainer, "_collect_resource_metrics"):
                    metrics = trainer.train()

                assert metrics is not None
