"""
Integration tests for the ModelTrainer class and its linked modules.

These tests focus on the integration between ModelTrainer and its dependencies:
- src.train.callbacks (<PERSON><PERSON>, <PERSON>back<PERSON><PERSON><PERSON>, ModelRunCallback)
- src.train.data_classes (TrainingComponents, TrainingConfig, TrainingMetrics)
- src.utils.device (select_device)
- src.utils.logging (setup_training_logger)

The tests verify that components work together correctly in realistic scenarios.
"""

# pylint: disable=duplicate-code

import json
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest
import torch
from torch import nn

from src.train.callbacks.early_stopping import EarlyStoppingCallback
from src.train.trainer import (
    DatabaseConfig,
    ModelTrainer,
    TrainerConfig,
    TrainerCreateParams,
)
from tests.integration.trainer_test_utils import (
    ModelComponentsConfig,
    create_mock_database_callback_setup,
    create_test_data_loaders,
    create_test_model_components,
    create_test_training_config,
)


class SimpleTestModel(nn.Module):
    """A simple model for integration testing."""

    def __init__(self, input_size=10, hidden_size=5, output_size=1):
        super().__init__()
        self.linear1 = nn.Linear(input_size, hidden_size)
        self.relu = nn.ReLU()
        self.linear2 = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        """Forward pass of the model."""
        x = self.linear1(x)
        x = self.relu(x)
        x = self.linear2(x)
        return x


@pytest.fixture(name="integration_data_loaders")
def fixture_integration_data_loaders():
    """Create realistic data loaders for integration testing."""

    return create_test_data_loaders(
        train_samples=64,
        test_samples=32,
        input_size=10,
        batch_size=8,
        binary_classification=True,
    )


@pytest.fixture(name="integration_model_components")
def fixture_integration_model_components():
    """Create realistic model components for integration testing."""

    config = ModelComponentsConfig(
        input_size=10,
        hidden_size=5,
        output_size=1,
        binary_classification=True,
        optimizer_type="adam",
        learning_rate=0.001,
    )
    return create_test_model_components(config)


@pytest.fixture(name="integration_training_config")
def fixture_integration_training_config():
    """Create training configuration for integration testing."""

    with tempfile.TemporaryDirectory() as temp_dir:
        yield create_test_training_config(
            temp_dir=temp_dir,
            model_id="integration_test_model",
            epochs=3,
            gradient_clip_max_norm=1.0,
            learning_rate_schedule=[
                {"epoch": 1, "rate": 0.0005},
                {"epoch": 2, "rate": 0.0001},
            ],
        )


class TestTrainerIntegration:
    """Integration tests for ModelTrainer with its dependencies."""

    def test_full_training_pipeline_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test complete training pipeline with all components integrated."""
        # Create trainer configuration
        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
        )

        # Initialize trainer
        trainer = ModelTrainer(config)

        # Verify initialization integrated correctly
        assert trainer.components.model is not None
        assert trainer.components.loss_fn is not None
        assert trainer.components.optimizer is not None
        assert trainer.data_loaders == integration_data_loaders
        assert trainer.device is not None
        assert trainer.logger is not None

        # Verify device integration (handle device index differences)
        model_device = next(trainer.components.model.parameters()).device
        trainer_device = trainer.device
        # Compare device types, ignoring index differences (e.g., mps:0 vs mps)
        assert model_device.type == trainer_device.type

        # Run training
        metrics = trainer.train()

        # Verify training completed successfully
        assert metrics is not None
        assert len(metrics.train_losses) == integration_training_config["epochs"]
        assert len(metrics.test_losses) == integration_training_config["epochs"]
        assert len(metrics.train_accuracies) == integration_training_config["epochs"]
        assert len(metrics.test_accuracies) == integration_training_config["epochs"]

        # Verify timing metrics were collected
        assert "start_time" in metrics.timing
        assert "end_time" in metrics.timing
        assert "total_training_time" in metrics.timing
        assert metrics.timing["total_training_time"] > 0

        # Verify artifacts were saved
        output_dir = Path(integration_training_config["run_output_dir"])
        assert (output_dir / "model.pt").exists()
        assert (output_dir / "metrics_history.json").exists()
        assert (output_dir / "metrics_summary.json").exists()

        # Verify metrics files contain expected data
        with open(output_dir / "metrics_summary.json", "r", encoding="utf-8") as f:
            summary = json.load(f)
            assert "final_train_loss" in summary
            assert "final_test_loss" in summary
            assert "timing" in summary

    def test_learning_rate_schedule_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test that learning rate schedule is properly integrated."""
        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
        )

        trainer = ModelTrainer(config)

        # Verify learning rate schedule was loaded
        assert len(trainer.lr_epoch_map) == 2
        assert trainer.lr_epoch_map[1] == 0.0005
        assert trainer.lr_epoch_map[2] == 0.0001

        # Mock the training methods to focus on LR schedule
        with patch.object(trainer, "_train_epoch") as mock_train, patch.object(
            trainer, "_validate_epoch"
        ) as mock_validate, patch.object(trainer, "_collect_resource_metrics"):

            mock_train.return_value = {"train_loss": 0.5, "train_accuracy": 0.8}
            mock_validate.return_value = {
                "validation_loss": 0.6,
                "validation_accuracy": 0.75,
            }

            # Track learning rate changes
            initial_lr = trainer.components.optimizer.param_groups[0]["lr"]
            lr_changes = []

            def track_lr(*args, **kwargs):  # pylint: disable=unused-argument
                current_lr = trainer.components.optimizer.param_groups[0]["lr"]
                lr_changes.append(current_lr)
                return mock_train.return_value

            mock_train.side_effect = track_lr

            trainer.train()

            # Verify learning rate was changed according to schedule
            assert len(lr_changes) == 3  # 3 epochs
            assert lr_changes[0] == initial_lr  # Epoch 0 (no change yet)
            assert lr_changes[1] == 0.0005  # Epoch 1 (first schedule change)
            assert lr_changes[2] == 0.0001  # Epoch 2 (second schedule change)

    def test_callback_system_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test integration of callback system with trainer."""
        # Create early stopping callback
        early_stopping = EarlyStoppingCallback(
            monitor="validation_loss", patience=1, verbose=True
        )

        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
            callbacks=[early_stopping],
        )

        trainer = ModelTrainer(config)

        # Verify callback was integrated
        assert len(trainer.callback_handler.callbacks) == 1
        assert isinstance(trainer.callback_handler.callbacks[0], EarlyStoppingCallback)
        assert trainer.callback_handler.callbacks[0].trainer == trainer

        # Mock validation to trigger early stopping
        validation_losses = [0.8, 0.9, 1.0]  # Increasing losses
        side_effects = [
            {"validation_loss": loss, "validation_accuracy": 0.5}
            for loss in validation_losses
        ]

        with patch.object(
            trainer, "_validate_epoch", side_effect=side_effects
        ), patch.object(
            trainer,
            "_train_epoch",
            return_value={"train_loss": 0.5, "train_accuracy": 0.8},
        ), patch.object(
            trainer, "_collect_resource_metrics"
        ):

            metrics = trainer.train()

            # Verify early stopping worked
            assert trainer.stop_training is True
            # Should stop after 2 epochs (baseline + 1 patience)
            assert len(metrics.train_losses) == 2

    def test_gradient_clipping_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test that gradient clipping is properly integrated."""
        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
        )

        trainer = ModelTrainer(config)

        # Verify gradient clipping config was loaded
        assert trainer.config.gradient_clip_max_norm == 1.0

        # Mock torch.nn.utils.clip_grad_norm_ to verify it's called
        with patch("torch.nn.utils.clip_grad_norm_") as mock_clip:
            # Run one epoch to test gradient clipping
            trainer._train_epoch(0)  # pylint: disable=protected-access

            # Verify gradient clipping was called
            assert mock_clip.call_count > 0
            # Verify it was called with correct parameters
            call_args = mock_clip.call_args_list[0]
            # The first argument should be the model parameters (passed as generator)
            # We can't directly compare generators, so check the call was made correctly
            assert call_args[1]["max_norm"] == 1.0

    def test_device_selection_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test that device selection is properly integrated."""
        # Mock select_device to return a specific device
        with patch("src.train.trainer.select_device") as mock_select_device:
            mock_device = torch.device("cpu")
            mock_select_device.return_value = mock_device

            config = TrainerConfig(
                model_components=integration_model_components,
                data_loaders=integration_data_loaders,
                training_config=integration_training_config,
            )

            trainer = ModelTrainer(config)

            # Verify device selection was called and used
            mock_select_device.assert_called_once()
            assert trainer.device == mock_device
            assert next(trainer.components.model.parameters()).device == mock_device

    def test_logging_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test that logging is properly integrated."""
        # Mock setup_training_logger to verify it's called correctly
        with patch("src.train.trainer.setup_training_logger") as mock_setup_logger:
            mock_logger = MagicMock()
            mock_setup_logger.return_value = mock_logger

            config = TrainerConfig(
                model_components=integration_model_components,
                data_loaders=integration_data_loaders,
                training_config=integration_training_config,
            )

            trainer = ModelTrainer(config)

            # Verify logger setup was called with correct parameters
            mock_setup_logger.assert_called_once_with(
                integration_training_config["run_output_dir"],
                integration_training_config["model_id"],
            )
            assert trainer.logger == mock_logger

            # Verify logger is used during training
            with patch.object(trainer, "_collect_resource_metrics"):
                trainer.train()

            # Verify logger was called during training
            assert mock_logger.info.call_count > 0

    def test_metrics_collection_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test that metrics collection is properly integrated across components."""
        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
        )

        trainer = ModelTrainer(config)

        # Add custom metrics to test integration
        trainer.add_custom_metric("test_metric", 1.0)
        trainer.add_custom_metric("test_metric", 2.0)

        # Mock resource collection to avoid system calls
        with patch.object(trainer, "_collect_resource_metrics") as mock_resource:

            def mock_collect():
                # Simulate resource metrics collection
                trainer.metrics.resources["cpu_percent"].append(50.0)
                trainer.metrics.resources["memory_percent"].append(60.0)

            mock_resource.side_effect = mock_collect

            metrics = trainer.train()

            # Verify all metric types were collected
            assert len(metrics.train_losses) == integration_training_config["epochs"]
            assert len(metrics.test_losses) == integration_training_config["epochs"]
            assert len(metrics.batch_metrics) > 0
            assert "test_metric" in metrics.custom_metrics
            assert metrics.custom_metrics["test_metric"] == [1.0, 2.0]
            assert len(metrics.resources["cpu_percent"]) > 0
            assert len(metrics.resources["memory_percent"]) > 0

            # Verify timing metrics
            assert "start_time" in metrics.timing
            assert "end_time" in metrics.timing
            assert "total_training_time" in metrics.timing
            assert (
                len(metrics.timing["epoch_times"])
                == integration_training_config["epochs"]
            )

    def test_error_handling_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test error handling integration across components."""
        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
        )

        trainer = ModelTrainer(config)

        # Test exception handling during training
        with patch.object(trainer, "_train_epoch") as mock_train:
            mock_train.side_effect = RuntimeError("Simulated training error")

            with patch.object(trainer, "_collect_resource_metrics"):
                # Training should handle the exception and save artifacts
                with pytest.raises(RuntimeError):
                    trainer.train()

                # Verify error was recorded in metrics
                assert trainer.metrics.error is not None
                assert trainer.metrics.error["type"] == "RuntimeError"
                assert "Simulated training error" in trainer.metrics.error["message"]

                # Verify artifacts were still saved on error
                output_dir = Path(integration_training_config["run_output_dir"])
                assert (output_dir / "metrics_history.json").exists()

        # Test KeyboardInterrupt handling
        trainer = ModelTrainer(config)  # Fresh trainer
        with patch.object(trainer, "_train_epoch") as mock_train:
            mock_train.side_effect = KeyboardInterrupt()

            with patch.object(trainer, "_collect_resource_metrics"):
                trainer.train()  # Should not raise

                # Verify interrupt was recorded
                assert trainer.metrics.error is not None
                assert trainer.metrics.error["type"] == "KeyboardInterrupt"


class TestTrainerDatabaseIntegration:
    """Integration tests for ModelTrainer with database components."""

    @patch("src.train.trainer.ModelRunCallback")
    def test_database_callback_integration(
        self,
        mock_callback_class,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test integration with ModelRunCallback for database updates."""
        # Setup mock callback using shared utilities

        mock_callback_instance, model_run_uuid, database_config = (
            create_mock_database_callback_setup()
        )
        mock_callback_class.return_value = mock_callback_instance

        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
            database_config=database_config,
        )

        trainer = ModelTrainer(config)

        # Verify ModelRunCallback was created and integrated
        mock_callback_class.assert_called_once_with(
            model_run_uuid=model_run_uuid,
            profile="development",
            verbose=True,
        )
        assert mock_callback_instance in trainer.callback_handler.callbacks

        # Mock training methods to focus on callback integration
        with patch.object(trainer, "_train_epoch") as mock_train, patch.object(
            trainer, "_validate_epoch"
        ) as mock_validate, patch.object(trainer, "_collect_resource_metrics"):

            mock_train.return_value = {"train_loss": 0.5, "train_accuracy": 0.8}
            mock_validate.return_value = {
                "validation_loss": 0.6,
                "validation_accuracy": 0.75,
            }

            trainer.train()

            # Verify callback lifecycle methods were called
            mock_callback_instance.on_train_begin.assert_called_once()
            mock_callback_instance.on_train_end.assert_called_once()
            assert (
                mock_callback_instance.on_epoch_end.call_count
                == integration_training_config["epochs"]
            )

    @patch("src.train.trainer.ModelRunCallback")
    def test_database_callback_import_error_handling(
        self,
        mock_callback_class,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test handling of ModelRunCallback import errors."""
        # Simulate import error
        mock_callback_class.side_effect = ImportError("Database service not available")

        database_config = DatabaseConfig(
            model_run_uuid=str(uuid4()), profile="development"
        )

        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
            database_config=database_config,
        )

        # Mock the logger to verify warning is logged
        with patch("src.train.trainer.setup_training_logger") as mock_setup_logger:
            mock_logger = MagicMock()
            mock_setup_logger.return_value = mock_logger

            trainer = ModelTrainer(config)

            # Verify warning was logged about failed import
            mock_logger.warning.assert_called_once()
            warning_call = mock_logger.warning.call_args[0][0]
            assert "Failed to import ModelRunCallback" in warning_call

            # Verify trainer still works without database callback
            assert len(trainer.callback_handler.callbacks) == 0

    def test_database_config_none_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test trainer works correctly without database configuration."""
        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
            database_config=None,
        )

        trainer = ModelTrainer(config)

        # Verify no database callbacks were added
        assert len(trainer.callback_handler.callbacks) == 0

        # Verify training still works
        with patch.object(trainer, "_collect_resource_metrics"):
            metrics = trainer.train()

        assert metrics is not None
        assert len(metrics.train_losses) == integration_training_config["epochs"]

    def test_database_config_partial_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test database config with missing model_run_uuid."""
        # Database config without model_run_uuid
        database_config = DatabaseConfig(model_run_uuid=None, profile="development")

        config = TrainerConfig(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
            database_config=database_config,
        )

        trainer = ModelTrainer(config)

        # Verify no database callbacks were added when UUID is None
        assert len(trainer.callback_handler.callbacks) == 0


class TestTrainerCreateMethod:
    """Integration tests for the ModelTrainer.create class method."""

    def test_create_method_integration(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test the create class method with TrainerCreateParams."""

        # Create params using the dataclass
        params = TrainerCreateParams(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
        )

        # Create trainer using class method
        trainer = ModelTrainer.create(params)

        # Verify trainer was created correctly
        assert trainer.components.model == integration_model_components["model"]
        assert trainer.data_loaders == integration_data_loaders
        assert trainer.config.config == integration_training_config

        # Verify training works
        with patch.object(trainer, "_collect_resource_metrics"):
            metrics = trainer.train()

        assert metrics is not None
        assert len(metrics.train_losses) == integration_training_config["epochs"]

    def test_create_method_with_database_config(
        self,
        integration_model_components,
        integration_data_loaders,
        integration_training_config,
    ):
        """Test create method with database configuration."""

        database_config = DatabaseConfig(model_run_uuid=str(uuid4()), profile="staging")

        params = TrainerCreateParams(
            model_components=integration_model_components,
            data_loaders=integration_data_loaders,
            training_config=integration_training_config,
            database_config=database_config,
        )

        with patch("src.train.trainer.ModelRunCallback") as mock_callback_class:
            mock_callback_instance = MagicMock()
            mock_callback_class.return_value = mock_callback_instance

            trainer = ModelTrainer.create(params)

            # Verify database callback was integrated
            mock_callback_class.assert_called_once()
            assert mock_callback_instance in trainer.callback_handler.callbacks


# pylint: enable=duplicate-code
