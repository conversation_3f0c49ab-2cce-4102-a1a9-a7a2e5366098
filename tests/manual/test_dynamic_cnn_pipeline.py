"""
Manual test script for the dynamic CNN training pipeline.

This script tests the end-to-end training process, including:
- Model creation using MLModelFactory
- Data loading (mocked)
- Training loop using ModelTrainer
- Saving of model checkpoints, metrics, and plots.

Run this script from the root of the project.
```
python tests/manual/test_dynamic_cnn_pipeline.py
```
"""

import os
import sys
import time

# This is a hack to get the tests to run from the command line
# It modifies sys.path to include the project root, allowing src imports.
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
sys.path.insert(0, project_root)

from src.models import MLModelFactory  # noqa: E402
from src.train.callbacks.model_checkpoint import ModelCheckpoint  # noqa: E402
from src.train.trainer import ModelTrainer, TrainerConfig  # noqa: E402
from src.utils.data_loader import data_loader, generate_gaussian_blurs  # noqa: E402
from src.utils.images import dataset_preview  # noqa: E402
from src.utils.plots import (  # noqa: E402
    plot_accuracy,
    plot_gaussian_blurs,
    plot_losses,
)

# Create Gaussian blurs with different widths
NUMBER_PER_CLASS = 1000
IMAGE_SIZE = 91

images, labels = generate_gaussian_blurs(IMAGE_SIZE, NUMBER_PER_CLASS)
dataset_preview(images, labels, number_per_class=NUMBER_PER_CLASS)
train_loader, test_loader = data_loader(images, labels, batch_size=32, test_size=0.1)

start_time = time.process_time()

model_factory = MLModelFactory(persistence_base_dir="src/models/trained")

# Define CNN model parameters (example, adjust as needed)
cnn_model_params = {
    "architecture": {
        "name": "CNN",
        "parameters": {
            "model_version": {
                "parameters": {
                    "convolutional_layers": [[16, 3, 1, 1], [32, 3, 1, 1]],
                    "fully_connected_layers": [64],
                    "activation": "relu",
                    "batch_norm": True,
                    "pooling": ["max", 2, 2],
                }
            },
            "model_run": {"parameters": {"dropout_rate": 0.3}},
            "image_size": IMAGE_SIZE,
            "image_channels": 1,  # Grayscale images from generate_gaussian_blurs
        },
    }
}
# Assuming binary classification (original vs. blurred), so num_classes=1 for BCEWithLogitsLoss
# or num_classes=2 for CrossEntropyLoss. The old CoinsNet often used num_classes=1 and BCE.
network = model_factory.create_model(
    architecture_params=cnn_model_params["architecture"], num_classes=1
)

# Create loss function
loss_params = {"type": "bce"}
loss_fn = model_factory.create_loss_function(loss_params)

# Create optimizer
optimizer_params = {"type": "adam", "learning_rate": 1e-3}
optimizer = model_factory.create_optimizer(optimizer_params, network.parameters())

# Define model run UUID and create directories for outputs
model_run_uuid = "trained_model_script"
base_output_dir = os.path.join("runs", model_run_uuid)
plots_dir = os.path.join(base_output_dir, "plots")
os.makedirs(plots_dir, exist_ok=True)

# Prepare training configuration
training_config = {
    "model_id": model_run_uuid,
    "run_output_dir": base_output_dir,
    "epochs": 10,
}

# Set up the ModelCheckpoint callback
checkpoint_path = os.path.join(base_output_dir, "best_model.pth")
checkpoint_callback = ModelCheckpoint(
    filepath=checkpoint_path, monitor="validation_loss", verbose=True, mode="min"
)

# Initialize and run the trainer
model_components = {
    "model": network,
    "loss_fn": loss_fn,
    "optimizer": optimizer,
}
data_loaders = {"train": train_loader, "test": test_loader}
config = TrainerConfig(
    model_components=model_components,
    data_loaders=data_loaders,
    training_config=training_config,
    callbacks=[checkpoint_callback],
)
trainer = ModelTrainer(config)
metrics = trainer.train()

# Retrieve results from the trainer
train_loss = metrics.train_losses
test_loss = metrics.test_losses
train_accuracy = metrics.train_accuracies
test_accuracy = metrics.test_accuracies
network = (
    trainer.components.model.cpu()
)  # Get the model back to CPU for saving/inference

end_time = 1000 * (time.process_time() - start_time)

print(f"End time: {end_time:.2f} ms")

# The ModelTrainer now handles saving the checkpoint and metrics internally.

# Show plots with results by saving them to files
plot_losses(train_loss, test_loss, output_path=os.path.join(plots_dir, "losses.png"))
plot_accuracy(
    train_accuracy, test_accuracy, output_path=os.path.join(plots_dir, "accuracy.png")
)

X, y = next(iter(test_loader))  # extract X,y from test dataloader
yHat = network(X)  # Dynamic CNN returns only the output tensor

# Display gaussian blurs by saving the plot to a file
plot_gaussian_blurs(
    y, yHat, X, output_path=os.path.join(plots_dir, "gaussian_blurs.png")
)

print(f"Plots saved to {plots_dir}")
